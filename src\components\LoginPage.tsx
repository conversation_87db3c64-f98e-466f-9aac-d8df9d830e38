import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowRight } from "lucide-react";
import loginBg from "@/assets/login_bg.jpg";
import logo from "@/assets/logo.png";

interface LoginPageProps {
  onLogin: (role: string) => void;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  const roles = [
    {
      value: "learner",
      label: "Learner",
      description: "Healthcare professional seeking training",
    },
    {
      value: "trainer",
      label: "Trainer",
      description: "Training program instructor",
    },
    {
      value: "manager",
      label: "Manager",
      description: "Department head or supervisor",
    },
    {
      value: "leadership",
      label: "Leadership",
      description: "Senior leadership team",
    },
    {
      value: "administrator",
      label: "Administrator",
      description: "System administrator",
    },
  ];

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedRole && credentials.email && credentials.password) {
      onLogin(selectedRole);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Background Image with Overlay Text */}
      <div className="hidden lg:flex lg:w-1/2 relative">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${loginBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />
        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/50" />
        
        {/* Content overlay */}
        <div className="relative z-10 flex flex-col justify-between h-full p-12 text-white">
          {/* Top - Brand name (centered) */}
          <div className="flex justify-center items-center pt-8">
            <h1 className="text-4xl font-bold text-center">Namaa by Aster</h1>
          </div>

          {/* Bottom - Tagline (centered) */}
          <div className="flex justify-center items-center pb-8">
            <p className="text-lg font-medium italic text-center">
             With knowledge and action, we elevate care
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md">
          {/* Logo and Welcome */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <img
                src={logo}
                alt="Medcare Logo"
                className="h-24 w-auto object-contain"
              />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Welcome
            </h2>
            <p className="text-gray-600 text-sm">
              Sign in to access your personalized dashboard
            </p>
          </div>

          {/* Login Form */}
          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-0 rounded-2xl overflow-hidden">


            <CardHeader className="px-8 py-6">
              <div className="space-y-6">
                {/* Role Selection */}
                <div className="space-y-3">
                  <Label htmlFor="role" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Select Role
                  </Label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger id="role" className="w-full h-12 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 rounded-xl transition-all duration-200 bg-gray-50/50">
                      <SelectValue placeholder="Choose your role" />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl border-2 shadow-lg">
                      {roles.map((role) => (
                        <SelectItem key={role.value} value={role.value} className="py-3 hover:bg-blue-50">
                          <div>
                            <div className="font-medium">{role.label}</div>
                            <div className="text-xs text-gray-500">
                              {role.description}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Email id */}
                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={credentials.email}
                    onChange={(e) =>
                      setCredentials((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    className="w-full h-12 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 rounded-xl transition-all duration-200 bg-gray-50/50 px-4"
                    required
                  />
                </div>

                {/* Password */}
                <div className="space-y-3">
                  <Label htmlFor="password" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={credentials.password}
                    onChange={(e) =>
                      setCredentials((prev) => ({
                        ...prev,
                        password: e.target.value,
                      }))
                    }
                    className="w-full h-12 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100 rounded-xl transition-all duration-200 bg-gray-50/50 px-4"
                    required
                  />
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="px-8 pb-8 pt-0">
              <form onSubmit={handleLogin} className="space-y-6">
                {/* Get Started Button */}
                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                  disabled={
                    !selectedRole ||
                    !credentials.email ||
                    !credentials.password
                  }
                >
                  Get Started
                  <ArrowRight className="w-5 h-5" />
                </Button>

                {/* Additional Links */}
                <div className="text-center space-y-4">
                  <button
                    type="button"
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
                  >
                    Forgot password?
                  </button>
                  <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                    <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                    <span>Need help? Contact IT Support</span>
                    <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="mt-8 text-center text-xs text-gray-500">
            <p>© 2025 Medcare LMS. All rights reserved.</p>
            <p className="mt-1">Secure • Professional • Trusted</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
